// utils
import { useFormContext } from 'react-hook-form';
import { Check } from 'lucide-react';
// components
import { FormField } from '@/components/ui/form';
// utils
import { cn } from '@/lib/utils';
import { RIcon, type TNameIcons } from '@tools/reactor-icons';

// ----------------------------------------------------------------------

interface CheckboxCardProps {
	name: string;
	label: string;
	iconName?: TNameIcons;
	className?: string;
	disabled?: boolean;
}

export function CheckboxCard({ name, label, iconName, className, disabled = false }: CheckboxCardProps) {
	const { control } = useFormContext();

	return (
		<FormField
			control={control}
			name={name}
			render={({ field }) => {
				const handleClick = (e: React.MouseEvent) => {
					e.preventDefault();
					e.stopPropagation();
					if (!disabled) {
						field.onChange(!field.value);
					}
				};

				return (
					<div
						className={cn(
							'flex flex-row items-start space-x-3 space-y-0 rounded-xl border p-5 cursor-pointer transition-colors hover:bg-accent/20 bg-card',
							field.value && 'bg-purple-50 border-primary hover:bg-purple-100',
							disabled && 'opacity-50 cursor-not-allowed',
							className
						)}
						onClick={handleClick}>
						<div
							className={cn(
								'h-4 w-4 shrink-0 rounded-sm border border-primary shadow flex items-center justify-center',
								field.value && 'bg-primary text-primary-foreground',
								disabled && 'cursor-not-allowed opacity-50'
							)}>
							{field.value && <Check className='h-4 w-4' />}
						</div>
						<div className='flex items-center leading-none gap-2 flex-1'>
							{iconName && <RIcon name={iconName} className='h-4 w-4' />}
							<span className='text-sm font-medium select-none'>{label}</span>
						</div>
					</div>
				);
			}}
		/>
	);
}
